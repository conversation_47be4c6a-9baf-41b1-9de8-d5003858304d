package models

import (
	"time"
)

// Episode represents a single episode in a series
type Episode struct {
	ID          int64     `json:"id" xorm:"pk autoincr 'id'"`
	VideoID     int64     `json:"video_id" xorm:"bigint notnull 'video_id'"` // Links to Video ID as series
	Title       string    `json:"title" xorm:"varchar(255) notnull 'title'"`
	Description string    `json:"description" xorm:"text 'description'"`
	EpisodeNum  int       `json:"episode_num" xorm:"int 'episode_num'"`      // Episode number in the series
	URL         string    `json:"url" xorm:"varchar(500) notnull 'url'"`     // Play URL for this episode
	Duration    int       `json:"duration" xorm:"int 'duration'"`            // Duration in seconds
	FileSize    int64     `json:"file_size" xorm:"bigint 'file_size'"`       // File size in bytes
	MimeType    string    `json:"mime_type" xorm:"varchar(100) 'mime_type'"`
	Thumbnail   string    `json:"thumbnail" xorm:"varchar(500) 'thumbnail'"` // Thumbnail URL for this episode
	Status      string    `json:"status" xorm:"varchar(20) notnull default('processing') 'status'"`
	CreatedAt   time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt   time.Time `json:"updated_at" xorm:"updated 'updated_at'"`
}