import React, { useState } from "react";
import { videosApi, PresignedURLRequest } from "../../api/videos";
import { episodesApi } from "../../api/episodes";

interface UploadEpisodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: number;
  onUploadComplete: () => void;
}

const UploadEpisodeModal: React.FC<UploadEpisodeModalProps> = ({
  isOpen,
  onClose,
  videoId,
  onUploadComplete,
}) => {
  const [episodeData, setEpisodeData] = useState({
    title: "",
    description: "",
  });
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setEpisodeData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type
      const validTypes = [
        "video/mp4",
        "video/avi",
        "video/mov",
        "video/wmv",
        "video/webm",
      ];
      if (!validTypes.includes(selectedFile.type)) {
        alert("Please select a valid video file (MP4, AVI, MOV, WMV, WebM)");
        return;
      }

      // Validate file size (500MB limit)
      const maxSize = 10000 * 1024 * 1024; // 10GB
      if (selectedFile.size > maxSize) {
        alert("File size must be less than 10GB");
        return;
      }

      setFile(selectedFile);
      
      // Auto-fill title if not already set
      if (!episodeData.title) {
        const fileName = selectedFile.name.replace(/\.[^/.]+$/, ""); // Remove extension
        setEpisodeData(prev => ({ ...prev, title: fileName }));
      }
    }
  };

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!episodeData.title || !file) {
      alert("Please fill in all required fields");
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      // Get presigned URL
      const presignedData: PresignedURLRequest = {
        video_id: videoId,
        filename: file.name,
        file_size: file.size,
        file_type: file.type,
      };

      const response = await videosApi.generatePresignedURL(presignedData);

      // Upload file directly to S3 or local storage
      if (response.url && response.url.includes('http')) {
        // S3 or external storage - direct upload
        // Create a custom upload progress tracker
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += 10;
          if (progress >= 90) {
            clearInterval(progressInterval);
            progress = 90; // Cap at 90% until upload completes
          }
          setUploadProgress(progress);
        }, 200);

        // Perform direct upload to S3
        const uploadResponse = await fetch(response.url, {
          method: "PUT",
          headers: {
            "Content-Type": file.type,
          },
          body: file,
        });

        clearInterval(progressInterval);

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed with status ${uploadResponse.status}`);
        }

        setUploadProgress(100);
      } else {
        // Local storage - upload through our API
        // Create a custom upload progress tracker
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += 15;
          if (progress >= 90) {
            clearInterval(progressInterval);
            progress = 90; // Cap at 90% until upload completes
          }
          setUploadProgress(progress);
        }, 300);

        try {
          // Upload through our episode API
          await episodesApi.uploadEpisodeFile(videoId, file, episodeData.title, episodeData.description);
          clearInterval(progressInterval);
          setUploadProgress(100);
        } catch (error) {
          clearInterval(progressInterval);
          throw error;
        }
      }

      // Show success state
      setUploadSuccess(true);
      setUploading(false);

      // Show success message briefly before closing
      setTimeout(() => {
        onClose();
        resetForm();
        onUploadComplete();
      }, 1500);
    } catch (error: any) {
      console.error("Upload error:", error);
      setUploadProgress(0);

      // Provide more specific error messages
      let errorMessage = "Failed to upload episode";
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
      setUploading(false);
    }
  };

  const resetForm = () => {
    setEpisodeData({ title: "", description: "" });
    setFile(null);
    setUploading(false);
    setUploadProgress(0);
    setUploadSuccess(false);
  };

  const handleClose = () => {
    if (!uploading) {
      onClose();
      resetForm();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg mx-4 overflow-hidden">
        {/* Modal header */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Upload Episode</h2>
            <button
              onClick={handleClose}
              disabled={uploading}
              className="text-white hover:text-gray-200 disabled:opacity-50 transition-colors"
            >
              <i className="fas fa-times text-2xl"></i>
            </button>
          </div>
        </div>

        <div className="p-6">
          <form onSubmit={handleUpload}>
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Episode Title *
              </label>
              <input
                type="text"
                name="title"
                value={episodeData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                placeholder="Enter episode title"
                required
              />
            </div>

            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Description
              </label>
              <textarea
                name="description"
                value={episodeData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                placeholder="Enter episode description"
              />
            </div>

            <div className="mb-8">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Select Video File *
              </label>
              <div className="flex items-center justify-center w-full">
                <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                  {file ? (
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <i className="fas fa-file-video text-4xl text-indigo-500 mb-3"></i>
                      <p className="text-sm font-medium text-gray-700 text-center px-4">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {(file.size / (1024 * 1024)).toFixed(2)} MB
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-3"></i>
                      <p className="text-sm text-gray-500">
                        <span className="font-semibold">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        MP4, AVI, MOV, WMV, WebM (Max: 500MB)
                      </p>
                    </div>
                  )}
                  <input
                    type="file"
                    accept="video/*"
                    onChange={handleFileChange}
                    disabled={uploading}
                    className="hidden"
                    required
                  />
                </label>
              </div>
            </div>

            {(uploading || uploadSuccess) && (
              <div className="mb-6">
                <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
                  <span>{uploadSuccess ? "Upload Complete!" : "Uploading..."}</span>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      uploadSuccess
                        ? "bg-gradient-to-r from-green-500 to-emerald-500"
                        : "bg-gradient-to-r from-indigo-500 to-purple-500"
                    }`}
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {uploadSuccess
                    ? "Episode uploaded successfully! Closing in a moment..."
                    : "Please do not close this window while uploading"
                  }
                </p>
              </div>
            )}

            <div className="flex justify-between">
              <button
                type="button"
                onClick={handleClose}
                disabled={uploading}
                className="px-6 py-3 text-gray-600 font-medium rounded-lg hover:bg-gray-100 disabled:opacity-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!file || uploading || uploadSuccess}
                className={`px-6 py-3 text-white font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-md hover:shadow-lg ${
                  uploadSuccess
                    ? "bg-gradient-to-r from-green-500 to-emerald-600"
                    : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                }`}
              >
                {uploadSuccess ? (
                  <>
                    <i className="fas fa-check mr-2"></i>
                    Upload Complete
                  </>
                ) : uploading ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Uploading...
                  </>
                ) : (
                  <>
                    <i className="fas fa-upload mr-2"></i>
                    Upload Episode
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UploadEpisodeModal;