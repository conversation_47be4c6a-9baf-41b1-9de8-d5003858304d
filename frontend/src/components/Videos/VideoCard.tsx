import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { VideoWithDetails } from '../../types';
import { deleteVideo, updateVideo } from '../../store/videosSlice';
import { incrementViews } from '../../store/videosSlice';

interface VideoCardProps {
  video: VideoWithDetails;
}

const VideoCard: React.FC<VideoCardProps> = React.memo(({ video }) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Handle click outside to close menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node) && 
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  const formatDuration = useCallback((seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes < 60) return `${minutes}m ${remainingSeconds}s`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  }, []);

  const handlePlay = () => {
    dispatch(incrementViews(video.id));
    navigate(`/videos/${video.id}/episodes`);
  };

  const handleViewEpisodes = () => {
    console.log('Navigating to episodes page for video ID:', video.id); // Debug log
    navigate(`/videos/${video.id}/episodes`);
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this video?')) {
      try {
        await dispatch(deleteVideo(video.id)).unwrap();
      } catch (error) {
        console.error('Failed to delete video:', error);
        alert('Failed to delete video');
      }
    }
  };

  const handleTogglePublish = async () => {
    try {
      const newStatus = video.status === 'published' ? 'private' : 'published';
      await dispatch(updateVideo({ 
        id: video.id, 
        data: { status: newStatus } 
      })).unwrap();
    } catch (error) {
      console.error('Failed to update video status:', error);
      alert('Failed to update video status');
    }
  };

  // Determine if user can edit this video
  const canEdit = user?.role === 'admin' || user?.id === video.user_id;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group">
      {/* Thumbnail */}
      <div className="relative">
        <div 
          className="w-full bg-gray-200 border-2 border-dashed rounded-t-lg overflow-hidden cursor-pointer"
          style={{ paddingBottom: '56.25%', height: 0 }}
          onClick={handlePlay}
        >
          {video.thumbnail ? (
            <img 
              src={video.thumbnail} 
              alt={video.title}
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
              <i className="fas fa-film text-gray-400 text-4xl"></i>
            </div>
          )}
        </div>

        {/* Play Button Overlay */}
        <div 
          className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center cursor-pointer"
          onClick={handlePlay}
        >
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
              <i className="fas fa-play text-2xl text-gray-800 ml-1"></i>
            </div>
          </div>
        </div>

        {/* Duration Badge */}
        {((video.episodes && video.episodes.length > 0 && video.episodes[0].duration > 0) ? video.episodes[0].duration : 0) > 0 && (
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {formatDuration((video.episodes && video.episodes.length > 0 && video.episodes[0].duration > 0) ? video.episodes[0].duration : 0)}
          </div>
        )}

        {/* Episodes Badge */}
        {video.episodes && video.episodes.length > 0 && (
          <div className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
            {video.episodes.length} {video.episodes.length === 1 ? 'Episode' : 'Episodes'}
          </div>
        )}
      </div>

      {/* Video Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2 flex-1">
            {video.title}
          </h3>
          
          {/* Menu Button */}
          {canEdit && (
            <div className="relative ml-2">
              <button
                ref={buttonRef}
                onClick={() => setShowMenu(!showMenu)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="fas fa-ellipsis-v"></i>
              </button>
              
              {/* Dropdown Menu */}
              {showMenu && (
                <div 
                  ref={menuRef}
                  className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10"
                >
                  <button
                    onClick={handleViewEpisodes}
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <i className="fas fa-list mr-2"></i>
                    Manage Episodes
                  </button>
                  <button
                    onClick={handleTogglePublish}
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                  >
                    <i className={`fas ${video.status === 'published' ? 'fa-eye-slash' : 'fa-eye'} mr-2`}></i>
                    {video.status === 'published' ? 'Make Private' : 'Publish'}
                  </button>
                  <button
                    onClick={handleDelete}
                    className="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                  >
                    <i className="fas fa-trash mr-2"></i>
                    Delete
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="flex items-center text-xs text-gray-500">
          <span>
            <i className="fas fa-eye mr-1"></i>
            {video.views}
          </span>
          <span className="mx-2">•</span>
          <span>
            <i className="fas fa-tag mr-1"></i>
            {video.category?.name || 'Uncategorized'}
          </span>
        </div>
        
        <div className="mt-2 flex items-center text-xs text-gray-500">
          <span>
            <i className="fas fa-user mr-1"></i>
            {video.user?.username || 'Unknown'}
          </span>
          <span className="mx-2">•</span>
          <span>
            <i className="fas fa-calendar mr-1"></i>
            {new Date(video.created_at).toLocaleDateString()}
          </span>
        </div>
        
        <div className="mt-3 flex justify-between">
          <span className={`px-2 py-1 rounded-full text-xs ${
            video.status === 'published' ? 'bg-green-100 text-green-800' :
            video.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {video.status}
          </span>
          
          {canEdit && (
            <button
              onClick={handleViewEpisodes}
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              <i className="fas fa-list mr-1"></i>
              Episodes
            </button>
          )}
        </div>
      </div>
    </div>
  );
});

export default VideoCard;