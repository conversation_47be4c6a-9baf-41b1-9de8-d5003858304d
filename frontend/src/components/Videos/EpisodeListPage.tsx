import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchVideo } from '../../store/videosSlice';
import { VideoWithDetails, Episode } from '../../types';
import EpisodeList from './EpisodeList';
import EpisodePlayerModal from './EpisodePlayerModal';
import UploadEpisodeModal from './UploadEpisodeModal';

const EpisodeListPage: React.FC = () => {
  const { videoId } = useParams<{ videoId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentVideo } = useAppSelector((state) => state.videos);
  
  const [selectedEpisode, setSelectedEpisode] = useState<Episode | null>(null);
  const [showEpisodePlayer, setShowEpisodePlayer] = useState(false);
  const [showUploadEpisode, setShowUploadEpisode] = useState(false);
  const [video, setVideo] = useState<VideoWithDetails | null>(null);

  console.log('EpisodeListPage rendered with videoId:', videoId); // Debug log

  useEffect(() => {
    if (videoId) {
      console.log('Fetching video with ID:', videoId); // Debug log
      dispatch(fetchVideo(parseInt(videoId)))
        .then((result) => {
          console.log('Video fetched successfully:', result.payload); // Debug log
          setVideo(result.payload as VideoWithDetails);
        })
        .catch((error) => {
          console.error('Failed to fetch video:', error);
        });
    }
  }, [dispatch, videoId]);

  const handlePlayEpisode = (episode: Episode) => {
    setSelectedEpisode(episode);
    setShowEpisodePlayer(true);
  };

  const handleUploadEpisode = () => {
    setShowUploadEpisode(true);
  };

  const handleUploadComplete = () => {
    if (videoId) {
      dispatch(fetchVideo(parseInt(videoId)))
        .then((result) => {
          setVideo(result.payload as VideoWithDetails);
        })
        .catch((error) => {
          console.error('Failed to refresh video:', error);
        });
    }
    setShowUploadEpisode(false);
  };

  const handleBack = () => {
    navigate('/videos');
  };

  if (!video && !currentVideo) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  const displayVideo = video || currentVideo;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header Section */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-all duration-200"
              >
                <i className="fas fa-arrow-left mr-2"></i>
                Back to Videos
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {displayVideo?.title}
                </h1>
                <p className="text-sm text-gray-500 mt-1">
                  {displayVideo?.episodes?.length || 0} episode{(displayVideo?.episodes?.length || 0) !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
            <button
              onClick={handleUploadEpisode}
              className="inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105"
            >
              <i className="fas fa-plus mr-2"></i>
              Add Clip to Collection
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {displayVideo && (
          <EpisodeList
            episodes={displayVideo.episodes || []}
            onPlayEpisode={handlePlayEpisode}
            onUploadEpisode={handleUploadEpisode}
            videoId={displayVideo.id}
          />
        )}
      </div>

      {/* Modals */}
      <EpisodePlayerModal
        episode={selectedEpisode}
        isOpen={showEpisodePlayer}
        onClose={() => setShowEpisodePlayer(false)}
      />

      <UploadEpisodeModal
        isOpen={showUploadEpisode}
        onClose={() => setShowUploadEpisode(false)}
        videoId={displayVideo?.id || 0}
        onUploadComplete={handleUploadComplete}
      />
    </div>
  );
};

export default EpisodeListPage;