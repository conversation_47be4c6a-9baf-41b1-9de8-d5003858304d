import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchVideo } from '../../store/videosSlice';
import { VideoWithDetails, Episode } from '../../types';
import EpisodeList from './EpisodeList';
import EpisodePlayerModal from './EpisodePlayerModal';
import UploadEpisodeModal from './UploadEpisodeModal';

const EpisodeListPage: React.FC = () => {
  const { videoId } = useParams<{ videoId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentVideo } = useAppSelector((state) => state.videos);
  
  const [selectedEpisode, setSelectedEpisode] = useState<Episode | null>(null);
  const [showEpisodePlayer, setShowEpisodePlayer] = useState(false);
  const [showUploadEpisode, setShowUploadEpisode] = useState(false);
  const [video, setVideo] = useState<VideoWithDetails | null>(null);

  console.log('EpisodeListPage rendered with videoId:', videoId); // Debug log

  useEffect(() => {
    if (videoId) {
      console.log('Fetching video with ID:', videoId); // Debug log
      dispatch(fetchVideo(parseInt(videoId)))
        .then((result) => {
          console.log('Video fetched successfully:', result.payload); // Debug log
          setVideo(result.payload as VideoWithDetails);
        })
        .catch((error) => {
          console.error('Failed to fetch video:', error);
        });
    }
  }, [dispatch, videoId]);

  const handlePlayEpisode = (episode: Episode) => {
    setSelectedEpisode(episode);
    setShowEpisodePlayer(true);
  };

  const handleUploadEpisode = () => {
    setShowUploadEpisode(true);
  };

  const handleUploadComplete = () => {
    if (videoId) {
      dispatch(fetchVideo(parseInt(videoId)))
        .then((result) => {
          setVideo(result.payload as VideoWithDetails);
        })
        .catch((error) => {
          console.error('Failed to refresh video:', error);
        });
    }
    setShowUploadEpisode(false);
  };

  const handleBack = () => {
    navigate('/videos');
  };

  if (!video && !currentVideo) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  const displayVideo = video || currentVideo;

  return (
    <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <button
              onClick={handleBack}
              className="flex items-center text-indigo-600 hover:text-indigo-800 mb-2"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Videos
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              Episodes for "{displayVideo?.title}"
            </h1>
          </div>
          <button
            onClick={handleUploadEpisode}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i className="fas fa-upload mr-2"></i>
            Upload Episode
          </button>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          {displayVideo && (
            <EpisodeList
              episodes={displayVideo.episodes || []}
              onPlayEpisode={handlePlayEpisode}
              onUploadEpisode={handleUploadEpisode}
              videoId={displayVideo.id}
            />
          )}
        </div>

        <EpisodePlayerModal
          episode={selectedEpisode}
          isOpen={showEpisodePlayer}
          onClose={() => setShowEpisodePlayer(false)}
        />

        <UploadEpisodeModal
          isOpen={showUploadEpisode}
          onClose={() => setShowUploadEpisode(false)}
          videoId={displayVideo?.id || 0}
          onUploadComplete={handleUploadComplete}
        />
      </div>
  );
};

export default EpisodeListPage;