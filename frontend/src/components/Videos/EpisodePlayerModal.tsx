import React, { useEffect, useRef } from 'react';
import { Episode } from '../../types';

interface EpisodePlayerModalProps {
  episode: Episode | null;
  isOpen: boolean;
  onClose: () => void;
}

const EpisodePlayerModal: React.FC<EpisodePlayerModalProps> = ({ 
  episode, 
  isOpen, 
  onClose 
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen && videoRef.current && episode) {
      videoRef.current.focus();
    }
  }, [isOpen, episode]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === modalRef.current) {
      onClose();
    }
  };

  if (!isOpen || !episode) return null;

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
      onClick={handleOverlayClick}
    >
      <div className="relative w-full max-w-6xl mx-4">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute -top-12 right-0 text-white hover:text-gray-300 text-2xl z-10"
        >
          <i className="fas fa-times"></i>
        </button>

        {/* Video Container */}
        <div className="relative bg-black rounded-lg overflow-hidden" style={{ paddingBottom: '56.25%', height: 0 }}>
          {episode.url ? (
            <video
              ref={videoRef}
              className="absolute top-0 left-0 w-full h-full"
              controls
              autoPlay
              src={episode.url}
            >
              Your browser does not support the video tag.
            </video>
          ) : (
            <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center text-white">
              <div className="text-center">
                <i className="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p>Episode file not available</p>
              </div>
            </div>
          )}
        </div>

        {/* Episode Info */}
        <div className="mt-4 text-white">
          <h2 className="text-xl font-bold mb-2">{episode.title}</h2>
          {episode.description && (
            <p className="text-gray-300 text-sm">{episode.description}</p>
          )}
          <div className="flex items-center mt-2 text-sm text-gray-400">
            {episode.duration > 0 && (
              <span className="mr-4">
                <i className="fas fa-clock mr-1"></i>
                {formatDuration(episode.duration)}
              </span>
            )}
            <span>
              <i className="fas fa-calendar mr-1"></i>
              {new Date(episode.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EpisodePlayerModal;