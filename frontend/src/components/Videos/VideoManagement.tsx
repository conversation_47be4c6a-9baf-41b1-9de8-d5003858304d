import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchVideos } from '../../store/videosSlice';
import { fetchActiveCategories } from '../../store/categoriesSlice';
import VideoGrid from './VideoGrid';
import VideoFilters from './VideoFilters';
import UploadVideoModal from './UploadVideoModal';
import { VideoWithDetails } from '../../types';

const VideoManagement: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { videos, loading, pagination } = useAppSelector((state) => state.videos);
  const { activeCategories } = useAppSelector((state) => state.categories);

  const [showUploadModal, setShowUploadModal] = useState(false);
  const [filters, setFilters] = useState({
    category_id: 0,
    status: '',
    page: 1,
    limit: 12,
  });

  // Fetch categories only once when component mounts
  useEffect(() => {
    dispatch(fetchActiveCategories());
  }, [dispatch]);

  // Ensure we have a valid array of categories
  const validCategories = Array.isArray(activeCategories) ? activeCategories : [];

  // Fetch videos when filters change
  useEffect(() => {
    dispatch(fetchVideos(filters));
  }, [dispatch, filters]);

  const handleFilterChange = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  }, []);

  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({ ...prev, page }));
  }, []);

  // Ensure videos is always an array
  const validVideos = Array.isArray(videos) ? videos : [];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Video Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your videos and episodes
          </p>
        </div>
        <button
          onClick={() => setShowUploadModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <i className="fas fa-upload mr-2"></i>
          Upload Video
        </button>
      </div>

      <VideoFilters
        categories={validCategories}
        filters={filters}
        onFilterChange={handleFilterChange}
      />

      <VideoGrid
        videos={validVideos}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
      />

      <UploadVideoModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        categories={validCategories}
      />
    </div>
  );
};

export default VideoManagement;