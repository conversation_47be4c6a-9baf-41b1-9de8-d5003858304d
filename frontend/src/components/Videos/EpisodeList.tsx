import React, { useState } from 'react';
import { Episode } from '../../types';

interface EpisodeListProps {
  episodes: Episode[];
  onPlayEpisode: (episode: Episode) => void;
  onUploadEpisode?: (videoId: number) => void;
  videoId: number;
}

const EpisodeList: React.FC<EpisodeListProps> = ({ 
  episodes, 
  onPlayEpisode,
  onUploadEpisode,
  videoId
}) => {
  const [expandedEpisode, setExpandedEpisode] = useState<number | null>(null);

  const toggleEpisodeDetails = (episodeId: number) => {
    setExpandedEpisode(expandedEpisode === episodeId ? null : episodeId);
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (episodes.length === 0) {
    return (
      <div className="text-center py-20">
        <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 mb-6">
          <i className="fas fa-film text-indigo-500 text-3xl"></i>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No episodes yet</h3>
        <p className="text-gray-500 mb-8 max-w-md mx-auto">
          Start building your series by uploading your first episode. Your audience is waiting!
        </p>
        <div className="space-y-4">
          {onUploadEpisode && (
            <button
              onClick={() => onUploadEpisode(videoId)}
              className="inline-flex items-center px-8 py-4 border border-transparent shadow-lg text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105"
            >
              <i className="fas fa-plus mr-3"></i>
              Upload Your First Episode
            </button>
          )}
          <div className="text-sm text-gray-400">
            Supported formats: MP4, AVI, MOV, WMV
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Episodes Stats */}
      <div className="mb-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <i className="fas fa-play text-blue-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Clips</p>
              <p className="text-2xl font-bold text-gray-900">{episodes.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <i className="fas fa-check-circle text-green-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Published</p>
              <p className="text-2xl font-bold text-gray-900">
                {episodes.filter(ep => ep.status === 'published').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <i className="fas fa-clock text-purple-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Duration</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatDuration(episodes.reduce((total, ep) => total + ep.duration, 0))}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Episodes Grid */}
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
        {episodes.map((episode, index) => (
          <div
            key={episode.id}
            className="group bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:border-indigo-200 transition-all duration-300 transform hover:-translate-y-1 animate-fade-in-up"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Episode Number Badge */}
            <div className="absolute top-4 left-4 z-10">
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                EP {index + 1}
              </div>
            </div>

            {/* Episode Thumbnail */}
            <div className="relative pb-[56.25%] overflow-hidden"> {/* 16:9 Aspect Ratio */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200">
                {episode.thumbnail ? (
                  <img
                    src={episode.thumbnail}
                    alt={episode.title}
                    className="absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
                    <i className="fas fa-film text-5xl text-indigo-300 group-hover:text-indigo-400 transition-colors duration-300"></i>
                  </div>
                )}

                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                {/* Play Button */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={() => onPlayEpisode(episode)}
                    className="w-16 h-16 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-75 group-hover:scale-100 shadow-xl hover:bg-white hover:shadow-2xl"
                  >
                    <i className="fas fa-play text-2xl text-indigo-600 ml-1"></i>
                  </button>
                </div>

                {/* Duration Badge */}
                {episode.duration > 0 && (
                  <div className="absolute bottom-3 right-3 bg-black/80 backdrop-blur-sm text-white text-xs font-medium px-2 py-1 rounded-lg">
                    {formatDuration(episode.duration)}
                  </div>
                )}

                {/* Status Badge */}
                <div className="absolute top-3 right-3">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    episode.status === 'published'
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : episode.status === 'processing'
                      ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                      : 'bg-gray-100 text-gray-800 border border-gray-200'
                  }`}>
                    <div className={`w-1.5 h-1.5 rounded-full mr-1 ${
                      episode.status === 'published' ? 'bg-green-400' :
                      episode.status === 'processing' ? 'bg-yellow-400' : 'bg-gray-400'
                    }`}></div>
                    {episode.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Episode Info */}
            <div className="p-6">
              <div className="flex justify-between items-start mb-3">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-indigo-600 transition-colors duration-200">
                  {episode.title}
                </h3>
                <button
                  onClick={() => toggleEpisodeDetails(episode.id)}
                  className="text-gray-400 hover:text-indigo-600 ml-2 p-1 rounded-full hover:bg-indigo-50 transition-all duration-200"
                >
                  <i className={`fas fa-chevron-${expandedEpisode === episode.id ? 'up' : 'down'} text-sm`}></i>
                </button>
              </div>

              {/* Episode Meta */}
              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                <div className="flex items-center">
                  <i className="fas fa-file-video mr-1.5 text-indigo-400"></i>
                  <span className="font-medium">
                    {episode.mime_type ? episode.mime_type.split('/')[1]?.toUpperCase() : 'Video'}
                  </span>
                </div>
                {episode.file_size > 0 && (
                  <div className="flex items-center">
                    <i className="fas fa-hdd mr-1.5 text-purple-400"></i>
                    <span>{formatFileSize(episode.file_size)}</span>
                  </div>
                )}
              </div>

              {/* Action Button */}
              <button
                onClick={() => onPlayEpisode(episode)}
                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <i className="fas fa-play mr-2"></i>
                Watch Episode
              </button>
            </div>

            {/* Expanded Details */}
            {expandedEpisode === episode.id && (
              <div className="px-6 pb-6 border-t border-gray-100 pt-4 bg-gray-50/50">
                <div className="space-y-4">
                  {/* Description */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                    {episode.description ? (
                      <p className="text-gray-700 text-sm leading-relaxed">{episode.description}</p>
                    ) : (
                      <p className="text-gray-500 italic text-sm">No description provided</p>
                    )}
                  </div>

                  {/* Metadata */}
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <i className="fas fa-calendar-plus text-blue-500 mr-2"></i>
                        <span className="text-sm font-medium text-gray-700">Created</span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {new Date(episode.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </span>
                    </div>

                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-gray-200">
                      <div className="flex items-center">
                        <i className="fas fa-sync text-green-500 mr-2"></i>
                        <span className="text-sm font-medium text-gray-700">Updated</span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {new Date(episode.updated_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EpisodeList;