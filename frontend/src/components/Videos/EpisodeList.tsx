import React, { useState } from 'react';
import { Episode } from '../../types';

interface EpisodeListProps {
  episodes: Episode[];
  onPlayEpisode: (episode: Episode) => void;
  onUploadEpisode?: (videoId: number) => void;
  videoId: number;
}

const EpisodeList: React.FC<EpisodeListProps> = ({ 
  episodes, 
  onPlayEpisode,
  onUploadEpisode,
  videoId
}) => {
  const [expandedEpisode, setExpandedEpisode] = useState<number | null>(null);

  const toggleEpisodeDetails = (episodeId: number) => {
    setExpandedEpisode(expandedEpisode === episodeId ? null : episodeId);
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (episodes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
          <i className="fas fa-film text-gray-400 text-xl"></i>
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">No episodes</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by uploading a new episode.</p>
        <div className="mt-6">
          {onUploadEpisode && (
            <button
              onClick={() => onUploadEpisode(videoId)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <i className="fas fa-plus mr-2"></i>
              Upload Episode
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {episodes.map((episode) => (
          <div 
            key={episode.id} 
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
          >
            {/* Episode Thumbnail */}
            <div className="relative pb-[56.25%]"> {/* 16:9 Aspect Ratio */}
              <div className="absolute inset-0 bg-gray-200 border-2 border-dashed rounded-t-lg flex items-center justify-center">
                {episode.thumbnail ? (
                  <img 
                    src={episode.thumbnail} 
                    alt={episode.title}
                    className="absolute inset-0 w-full h-full object-cover"
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
                    <i className="fas fa-film text-4xl text-indigo-300"></i>
                  </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                  <button
                    onClick={() => onPlayEpisode(episode)}
                    className="opacity-0 hover:opacity-100 transition-opacity duration-200 w-16 h-16 rounded-full bg-white bg-opacity-90 flex items-center justify-center"
                  >
                    <i className="fas fa-play text-2xl text-gray-800 ml-1"></i>
                  </button>
                </div>
                {episode.duration > 0 && (
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    {formatDuration(episode.duration)}
                  </div>
                )}
              </div>
            </div>

            {/* Episode Info */}
            <div className="p-4">
              <div className="flex justify-between items-start">
                <h3 className="text-sm font-medium text-gray-900 line-clamp-2">{episode.title}</h3>
                <button
                  onClick={() => toggleEpisodeDetails(episode.id)}
                  className="text-gray-400 hover:text-gray-600 ml-2"
                >
                  <i className={`fas fa-chevron-${expandedEpisode === episode.id ? 'up' : 'down'}`}></i>
                </button>
              </div>

              <div className="mt-2 flex items-center text-xs text-gray-500">
                <span className="mr-3">
                  <i className="fas fa-file-alt mr-1"></i>
                  {episode.mime_type ? episode.mime_type.split('/')[1]?.toUpperCase() : 'Unknown'}
                </span>
                {episode.file_size > 0 && (
                  <span>
                    <i className="fas fa-database mr-1"></i>
                    {formatFileSize(episode.file_size)}
                  </span>
                )}
              </div>

              <div className="mt-3 flex justify-between items-center">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  episode.status === 'published' ? 'bg-green-100 text-green-800' :
                  episode.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {episode.status}
                </span>
                <button
                  onClick={() => onPlayEpisode(episode)}
                  className="px-3 py-1 bg-indigo-500 text-white rounded hover:bg-indigo-600 transition-colors text-xs"
                >
                  <i className="fas fa-play mr-1"></i>
                  Play
                </button>
              </div>
            </div>

            {/* Expanded Details */}
            {expandedEpisode === episode.id && (
              <div className="px-4 pb-4 border-t border-gray-100 pt-4">
                {episode.description ? (
                  <p className="text-gray-700 text-sm">{episode.description}</p>
                ) : (
                  <p className="text-gray-500 italic text-sm">No description provided</p>
                )}
                <div className="mt-3 flex items-center text-xs text-gray-500">
                  <span className="mr-4">
                    <i className="fas fa-calendar mr-1"></i>
                    Created: {new Date(episode.created_at).toLocaleDateString()}
                  </span>
                  <span>
                    <i className="fas fa-sync mr-1"></i>
                    Updated: {new Date(episode.updated_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EpisodeList;