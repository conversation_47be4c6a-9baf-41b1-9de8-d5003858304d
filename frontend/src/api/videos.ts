import apiClient from "./client";
import {
  VideoWithDetails,
  CreateVideoRequest,
  UpdateVideoRequest,
  VideoStats,
  ApiResponse,
  PaginatedResponse,
} from "../types";

export interface PresignedURLRequest {
  video_id: number;
  filename: string;
  file_size: number;
  file_type: string;
}

export interface PresignedURLResponse {
  url: string;
  file_path: string;
}

interface VideosApi {
  // Get all videos with pagination and filtering
  getVideos: (params?: {
    page?: number;
    limit?: number;
    category_id?: number;
    status?: string;
    user_id?: number;
  }) => Promise<PaginatedResponse<VideoWithDetails>>;

  // Get video by array of key-value pairs
  getVideo: (videoData: Record<string, any>) => Promise<VideoWithDetails>;

  // Create new video
  createVideo: (data: CreateVideoRequest) => Promise<VideoWithDetails>;

  // Generate presigned URL for S3 upload
  generatePresignedURL: (
    data: PresignedURLRequest
  ) => Promise<PresignedURLResponse>;

  // Upload video file directly to server (fallback)
  uploadVideo: (id: number, file: File) => Promise<void>;

  // Upload episode file directly to server (for local storage)
  uploadEpisode: (videoId: number, file: File, title: string, description?: string) => Promise<any>;

  // Update video
  updateVideo: (
    id: number,
    data: UpdateVideoRequest
  ) => Promise<VideoWithDetails>;

  // Delete video
  deleteVideo: (id: number) => Promise<void>;

  // Increment view count
  incrementViews: (id: number) => Promise<void>;

  // Get video statistics
  getStats: () => Promise<VideoStats>;
}

export const videosApi: VideosApi = {
  // Get all videos with pagination and filtering
  getVideos: async (params = {}) => {
    const response = await apiClient.get<PaginatedResponse<VideoWithDetails>>(
      "/videos",
      { params }
    );
    return response.data;
  },

  // Get video by array of key-value pairs
  getVideo: async (videoData: Record<string, any>) => {
    const response = await apiClient.post<ApiResponse<VideoWithDetails>>(
      "/videos/detail",
      videoData
    );
    return response.data.data;
  },

  // Create new video
  createVideo: async (data: CreateVideoRequest) => {
    const response = await apiClient.post<ApiResponse<VideoWithDetails>>(
      "/videos",
      data
    );
    return response.data.data;
  },

  // Generate presigned URL for S3 upload
  generatePresignedURL: async (data: PresignedURLRequest) => {
    const response = await apiClient.post<ApiResponse<PresignedURLResponse>>(
      "/videos/presigned-url",
      data
    );
    return response.data.data;
  },

  // Upload video file directly to server (fallback)
  uploadVideo: async (id: number, file: File) => {
    const formData = new FormData();
    formData.append("video", file);

    await apiClient.post(`/videos/${id}/upload`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // Upload episode file directly to server (for local storage)
  uploadEpisode: async (videoId: number, file: File, title: string, description?: string) => {
    const formData = new FormData();
    formData.append("episode", file);
    formData.append("title", title);
    if (description) {
      formData.append("description", description);
    }

    const response = await apiClient.post<ApiResponse<any>>(`/videos/${videoId}/episode/upload`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  // Update video
  updateVideo: async (id: number, data: UpdateVideoRequest) => {
    const response = await apiClient.put<ApiResponse<VideoWithDetails>>(
      `/videos/${id}`,
      data
    );
    return response.data.data;
  },

  // Delete video
  deleteVideo: async (id: number) => {
    await apiClient.delete(`/videos/${id}`);
  },

  // Increment view count
  incrementViews: async (id: number) => {
    await apiClient.post(`/videos/${id}/view`);
  },

  // Get video statistics
  getStats: async () => {
    const response = await apiClient.get<ApiResponse<VideoStats>>(
      "/videos/stats"
    );
    return response.data.data;
  },
};
