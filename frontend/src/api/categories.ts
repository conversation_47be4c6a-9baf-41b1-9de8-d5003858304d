import apiClient from './client';
import { 
  Category, 
  CategoryWithStats, 
  CreateCategoryRequest, 
  UpdateCategoryRequest,
  ApiResponse,
  PaginatedResponse 
} from '../types';

export const categoriesApi = {
  // Get all categories with pagination
  getCategories: async (params: {
    page?: number;
    limit?: number;
    include_stats?: boolean;
  } = {}): Promise<PaginatedResponse<CategoryWithStats> | ApiResponse<CategoryWithStats[]>> => {
    const response = await apiClient.get('/categories', { params });
    return response.data;
  },

  // Get active categories (for dropdowns)
  getActiveCategories: async (): Promise<Category[]> => {
    const response = await apiClient.get<Category[]>('/categories/active');
    return response.data;
  },

  // Get category by ID
  getCategory: async (id: number, includeStats = false): Promise<CategoryWithStats> => {
    const response = await apiClient.get<ApiResponse<CategoryWithStats>>(`/categories/${id}`, {
      params: { include_stats: includeStats }
    });
    return response.data.data;
  },

  // Create new category (admin only)
  createCategory: async (data: CreateCategoryRequest): Promise<Category> => {
    const response = await apiClient.post<ApiResponse<Category>>('/admin/categories', data);
    return response.data.data;
  },

  // Update category (admin only)
  updateCategory: async (id: number, data: UpdateCategoryRequest): Promise<Category> => {
    const response = await apiClient.put<ApiResponse<Category>>(`/admin/categories/${id}`, data);
    return response.data.data;
  },

  // Delete category (admin only)
  deleteCategory: async (id: number): Promise<void> => {
    await apiClient.delete(`/admin/categories/${id}`);
  },

  // Get category statistics
  getStats: async (): Promise<{ total_categories: number; active_categories: number }> => {
    const response = await apiClient.get<ApiResponse<{ total_categories: number; active_categories: number }>>('/categories/stats');
    return response.data.data;
  },
};
