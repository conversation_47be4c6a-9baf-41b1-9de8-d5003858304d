import apiClient from './client';
import { Episode, ApiResponse } from '../types';

export interface CreateEpisodeRequest {
  video_id: number;
  title: string;
  description?: string;
  episode_num?: number;
}

export interface UpdateEpisodeRequest {
  title?: string;
  description?: string;
  episode_num?: number;
  status?: string;
}

interface EpisodesApi {
  // Get episodes by video ID
  getEpisodesByVideoId: (videoId: number) => Promise<Episode[]>;

  // Get episode by ID
  getEpisodeById: (id: number) => Promise<Episode>;

  // Create new episode
  createEpisode: (data: CreateEpisodeRequest) => Promise<Episode>;

  // Update episode
  updateEpisode: (id: number, data: UpdateEpisodeRequest) => Promise<Episode>;

  // Delete episode
  deleteEpisode: (id: number) => Promise<void>;

  // Upload episode file directly to server (for local storage)
  uploadEpisodeFile: (videoId: number, file: File, title: string, description?: string) => Promise<any>;
}

export const episodesApi: EpisodesApi = {
  // Get episodes by video ID
  getEpisodesByVideoId: async (videoId: number) => {
    const response = await apiClient.get<Episode[]>(`/episodes/video/${videoId}`);
    return response.data;
  },

  // Get episode by ID
  getEpisodeById: async (id: number) => {
    const response = await apiClient.get<Episode>(`/episodes/${id}`);
    return response.data;
  },

  // Create new episode
  createEpisode: async (data: CreateEpisodeRequest) => {
    const response = await apiClient.post<Episode>('/episodes/', data);
    return response.data;
  },

  // Update episode
  updateEpisode: async (id: number, data: UpdateEpisodeRequest) => {
    const response = await apiClient.put<Episode>(`/episodes/${id}`, data);
    return response.data;
  },

  // Delete episode
  deleteEpisode: async (id: number) => {
    await apiClient.delete(`/episodes/${id}`);
  },

  // Upload episode file directly to server (for local storage)
  uploadEpisodeFile: async (videoId: number, file: File, title: string, description?: string) => {
    const formData = new FormData();
    formData.append("episode", file);
    formData.append("title", title);
    if (description) {
      formData.append("description", description);
    }

    const response = await apiClient.post<ApiResponse<any>>(`/videos/${videoId}/episode/upload`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },
};
