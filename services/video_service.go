package services

import (
	"bytes"
	"fmt"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"

	"damoncoo/coremovie/database"
	"damoncoo/coremovie/models"
	"damoncoo/coremovie/storage"
)

// bytesReaderCloser wraps a bytes.Reader to implement the multipart.File interface
type bytesReaderCloser struct {
	*bytes.Reader
}

// Close implements the Close method for multipart.File interface
func (brc *bytesReaderCloser) Close() error {
	return nil // bytes.Reader doesn't need closing
}

// VideoService handles video-related business logic
type VideoService struct {
	storage storage.Storage
}

// NewVideoService creates a new VideoService instance
func NewVideoService() *VideoService {
	// Determine storage type from environment
	storageType := os.Getenv("STORAGE_TYPE")
	if storageType == "" {
		storageType = "local" // default to local storage
	}

	var store storage.Storage

	switch storage.StorageType(storageType) {
	case storage.S3Storage:
		// Configure S3 storage
		s3Config := storage.S3Config{
			Endpoint:        os.Getenv("S3_ENDPOINT"),
			Region:          os.Getenv("S3_REGION"),
			AccessKeyID:     os.Getenv("S3_ACCESS_KEY_ID"),
			SecretAccessKey: os.Getenv("S3_SECRET_ACCESS_KEY"),
			BucketName:      os.Getenv("S3_BUCKET_NAME"),
			UseSSL:          os.Getenv("S3_USE_SSL") == "true",
			AccessURL:       os.Getenv("S3_ACCESS_URL"),
		}

		s3Storage, err := storage.NewS3Storage(s3Config)
		if err != nil {
			panic(err)
		} else {
			store = s3Storage
		}
	default:
		// Default to local storage
		uploadPath := os.Getenv("UPLOAD_PATH")
		if uploadPath == "" {
			uploadPath = "./uploads"
		}
		store = storage.NewLocalStorage(
			uploadPath,
			os.Getenv("LOCAL_BASE_URL"),
		)
	}

	return &VideoService{
		storage: store,
	}
}

// GetAllVideos retrieves all videos with pagination and optional filtering
func (s *VideoService) GetAllVideos(page, limit int, categoryID int64, status string, userID int64) ([]models.VideoWithDetails, int64, error) {
	var videos []models.Video
	var total int64

	// Build query
	query := database.DB.Table("videos")

	// Apply filters
	if categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if userID > 0 {
		query = query.Where("user_id = ?", userID)
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get videos with pagination
	total, err := query.Limit(limit, offset).OrderBy("created_at DESC").FindAndCount(&videos)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get videos: %v", err)
	}

	// Get detailed video information
	var videoDetails []models.VideoWithDetails
	for _, video := range videos {
		detail, err := s.getVideoWithDetails(&video)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get video details: %v", err)
		}
		videoDetails = append(videoDetails, *detail)
	}

	return videoDetails, total, nil
}

// GetVideoByID retrieves a video by ID with details
func (s *VideoService) GetVideoByID(id int64) (*models.VideoWithDetails, error) {
	var video models.Video
	has, err := database.DB.ID(id).Get(&video)
	if err != nil {
		return nil, fmt.Errorf("failed to get video: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("video not found")
	}

	return s.getVideoWithDetails(&video)
}

// getVideoWithDetails gets video with category, user, and episode information
func (s *VideoService) getVideoWithDetails(video *models.Video) (*models.VideoWithDetails, error) {
	detail := &models.VideoWithDetails{
		Video: *video,
	}

	// Get category information
	if video.CategoryID > 0 {
		var category models.Category
		has, err := database.DB.ID(video.CategoryID).Get(&category)
		if err != nil {
			return nil, fmt.Errorf("failed to get category: %v", err)
		}
		if has {
			detail.Category = &category
		}
	}

	// Get user information
	var user models.User
	has, err := database.DB.ID(video.UserID).Get(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %v", err)
	}
	if has {
		userResponse := models.UserResponse{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			CreatedAt: user.CreatedAt,
		}
		detail.User = &userResponse
	}

	// Get episodes for this video
	episodeService := NewEpisodeService()
	episodes, err := episodeService.GetEpisodesByVideoID(video.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get episodes: %v", err)
	}
	detail.Episodes = episodes

	// If there are episodes, use the first one's URL as the main URL
	if len(episodes) > 0 && episodes[0].URL != "" {
		detail.URL = episodes[0].URL
	}

	return detail, nil
}

// CreateVideo creates a new video record
func (s *VideoService) CreateVideo(req *models.CreateVideoRequest, userID int64) (*models.Video, error) {
	// Verify category exists
	var category models.Category
	has, err := database.DB.ID(req.CategoryID).Get(&category)
	if err != nil {
		return nil, fmt.Errorf("failed to verify category: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("category not found")
	}

	// Create video record
	video := &models.Video{
		Title:       req.Title,
		Description: req.Description,
		CategoryID:  req.CategoryID,
		UserID:      userID,
		Status:      "processing",
		Views:       0,
	}

	_, err = database.DB.Insert(video)
	if err != nil {
		return nil, fmt.Errorf("failed to create video: %v", err)
	}

	return video, nil
}

// GeneratePresignedURL generates a presigned URL for direct S3 upload
func (s *VideoService) GeneratePresignedURL(req *models.PresignedURLRequest) (*storage.PresignedURLResponse, error) {
	// Get video record
	var video models.Video
	has, err := database.DB.ID(req.VideoID).Get(&video)
	if err != nil {
		return nil, fmt.Errorf("failed to get video: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("video not found")
	}

	// Validate file type
	if !s.isValidVideoFile(req.Filename) {
		return nil, fmt.Errorf("invalid video file type")
	}

	// Generate presigned URL using the configured storage
	presignedURL, err := s.storage.GeneratePresignedURL(req.VideoID, req.Filename, req.FileSize, req.FileType)
	if err != nil {
		return nil, fmt.Errorf("failed to generate presigned URL: %v", err)
	}

	// Instead of updating the video directly, we'll create or update an episode
	episodeService := NewEpisodeService()
	episode := &models.Episode{
		VideoID:  req.VideoID,
		Title:    fmt.Sprintf("Episode for %s", req.Filename),
		URL:      presignedURL.URL,
		FileSize: req.FileSize,
		MimeType: req.FileType,
		Status:   "published",
	}

	// Check if episode already exists for this video
	existingEpisodes, err := episodeService.GetEpisodesByVideoID(req.VideoID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing episodes: %v", err)
	}

	if len(existingEpisodes) > 0 {
		// Update existing episode
		episode.ID = existingEpisodes[0].ID
		err = episodeService.UpdateEpisode(episode)
		if err != nil {
			return nil, fmt.Errorf("failed to update episode: %v", err)
		}
	} else {
		// Create new episode
		err = episodeService.CreateEpisode(episode)
		if err != nil {
			return nil, fmt.Errorf("failed to create episode: %v", err)
		}
	}

	// Update video status to published
	video.Status = "published"
	_, err = database.DB.ID(req.VideoID).Update(&video)
	if err != nil {
		return nil, fmt.Errorf("failed to update video record: %v", err)
	}

	return presignedURL, nil
}

// UploadVideoFile handles video file upload (for local storage or fallback)
func (s *VideoService) UploadVideoFile(videoID int64, file *multipart.FileHeader) error {
	// Get video record
	var video models.Video
	has, err := database.DB.ID(videoID).Get(&video)
	if err != nil {
		return fmt.Errorf("failed to get video: %v", err)
	}
	if !has {
		return fmt.Errorf("video not found")
	}

	// Validate file type
	if !s.isValidVideoFile(file.Filename) {
		return fmt.Errorf("invalid video file type")
	}

	// Open uploaded file
	src, err := file.Open()
	if err != nil {
		return fmt.Errorf("failed to open uploaded file: %v", err)
	}
	defer src.Close()

	// Upload file using the configured storage
	metadata, err := s.storage.UploadFile(src, file, videoID)
	if err != nil {
		return fmt.Errorf("failed to upload file: %v", err)
	}

	// Instead of updating the video directly, we'll create or update an episode
	episodeService := NewEpisodeService()
	episode := &models.Episode{
		VideoID:  videoID,
		Title:    fmt.Sprintf("Episode for %s", metadata.Filename),
		URL:      s.storage.GetFileURL(metadata.Filepath),
		FileSize: metadata.Size,
		MimeType: metadata.ContentType,
		Status:   "published",
	}

	// Check if episode already exists for this video
	existingEpisodes, err := episodeService.GetEpisodesByVideoID(videoID)
	if err != nil {
		s.storage.DeleteFile(metadata.Filepath)
		return fmt.Errorf("failed to check existing episodes: %v", err)
	}

	if len(existingEpisodes) > 0 {
		// Update existing episode
		episode.ID = existingEpisodes[0].ID
		err = episodeService.UpdateEpisode(episode)
		if err != nil {
			s.storage.DeleteFile(metadata.Filepath)
			return fmt.Errorf("failed to update episode: %v", err)
		}
	} else {
		// Create new episode
		err = episodeService.CreateEpisode(episode)
		if err != nil {
			s.storage.DeleteFile(metadata.Filepath)
			return fmt.Errorf("failed to create episode: %v", err)
		}
	}

	// Update video status to published
	video.Status = "published"
	_, err = database.DB.ID(videoID).Update(&video)
	if err != nil {
		// Clean up uploaded file if database update fails
		s.storage.DeleteFile(metadata.Filepath)
		return fmt.Errorf("failed to update video record: %v", err)
	}

	return nil
}

// UploadVideoFileFromBytes handles video file upload from raw bytes
// This is used for direct uploads (like S3 presigned URLs) where we receive raw file data
func (s *VideoService) UploadVideoFileFromBytes(videoID int64, fileData []byte, filename, contentType string, fileSize int64) error {
	// Get video record
	var video models.Video
	has, err := database.DB.ID(videoID).Get(&video)
	if err != nil {
		return fmt.Errorf("failed to get video: %v", err)
	}
	if !has {
		return fmt.Errorf("video not found")
	}

	// Validate file type
	if !s.isValidVideoFile(filename) {
		return fmt.Errorf("invalid video file type")
	}

	// Create a bytes reader from the file data
	fileReader := &bytesReaderCloser{bytes.NewReader(fileData)}

	// Create a mock multipart.FileHeader for compatibility with storage interface
	fileHeader := &multipart.FileHeader{
		Filename: filename,
		Size:     fileSize,
		Header:   make(map[string][]string),
	}
	fileHeader.Header.Set("Content-Type", contentType)

	// Upload file using the configured storage
	metadata, err := s.storage.UploadFile(fileReader, fileHeader, videoID)
	if err != nil {
		return fmt.Errorf("failed to upload file: %v", err)
	}

	// Instead of updating the video directly, we'll create or update an episode
	episodeService := NewEpisodeService()
	episode := &models.Episode{
		VideoID:  videoID,
		Title:    fmt.Sprintf("Episode for %s", metadata.Filename),
		URL:      s.storage.GetFileURL(metadata.Filepath),
		FileSize: metadata.Size,
		MimeType: metadata.ContentType,
		Status:   "published",
	}

	// Check if episode already exists for this video
	existingEpisodes, err := episodeService.GetEpisodesByVideoID(videoID)
	if err != nil {
		s.storage.DeleteFile(metadata.Filepath)
		return fmt.Errorf("failed to check existing episodes: %v", err)
	}

	if len(existingEpisodes) > 0 {
		// Update existing episode
		episode.ID = existingEpisodes[0].ID
		err = episodeService.UpdateEpisode(episode)
		if err != nil {
			s.storage.DeleteFile(metadata.Filepath)
			return fmt.Errorf("failed to update episode: %v", err)
		}
	} else {
		// Create new episode
		err = episodeService.CreateEpisode(episode)
		if err != nil {
			s.storage.DeleteFile(metadata.Filepath)
			return fmt.Errorf("failed to create episode: %v", err)
		}
	}

	// Update video status to published
	video.Status = "published"
	_, err = database.DB.ID(videoID).Update(&video)
	if err != nil {
		// Clean up uploaded file if database update fails
		s.storage.DeleteFile(metadata.Filepath)
		return fmt.Errorf("failed to update video record: %v", err)
	}

	return nil
}

// isValidVideoFile checks if the file is a valid video file
func (s *VideoService) isValidVideoFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExtensions := []string{".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv"}
	for _, validExt := range validExtensions {
		if ext == validExt {
			return true
		}
	}
	return false
}

// UpdateVideo updates an existing video
func (s *VideoService) UpdateVideo(id int64, req *models.UpdateVideoRequest, userID int64, isAdmin bool) (*models.Video, error) {
	// Get existing video
	var video models.Video
	has, err := database.DB.ID(id).Get(&video)
	if err != nil {
		return nil, fmt.Errorf("failed to get video: %v", err)
	}
	if !has {
		return nil, fmt.Errorf("video not found")
	}

	// Check permissions - users can only update their own videos unless they're admin
	if !isAdmin && video.UserID != userID {
		return nil, fmt.Errorf("you can only update your own videos")
	}

	// Update fields if provided
	if req.Title != "" {
		video.Title = req.Title
	}

	if req.Description != "" {
		video.Description = req.Description
	}

	if req.CategoryID > 0 {
		// Verify category exists
		var category models.Category
		has, err := database.DB.ID(req.CategoryID).Get(&category)
		if err != nil {
			return nil, fmt.Errorf("failed to verify category: %v", err)
		}
		if !has {
			return nil, fmt.Errorf("category not found")
		}
		video.CategoryID = req.CategoryID
	}

	if req.Status != "" {
		video.Status = req.Status
	}

	// Update video
	_, err = database.DB.ID(id).Update(&video)
	if err != nil {
		return nil, fmt.Errorf("failed to update video: %v", err)
	}

	return &video, nil
}

// DeleteVideo deletes a video and its file
func (s *VideoService) DeleteVideo(id int64, userID int64, isAdmin bool) error {
	// Get existing video
	var video models.Video
	has, err := database.DB.ID(id).Get(&video)
	if err != nil {
		return fmt.Errorf("failed to get video: %v", err)
	}
	if !has {
		return fmt.Errorf("video not found")
	}

	// Check permissions - users can only delete their own videos unless they're admin
	if !isAdmin && video.UserID != userID {
		return fmt.Errorf("you can only delete your own videos")
	}

	// For series, delete all associated episodes
	episodeService := NewEpisodeService()
	episodes, err := episodeService.GetEpisodesByVideoID(id)
	if err != nil {
		return fmt.Errorf("failed to get episodes: %v", err)
	}

	// Delete episode files if they exist
	for _, episode := range episodes {
		if episode.URL != "" {
			// Note: In a real implementation, you might want to parse the URL to determine
			// if it's a local file that needs deletion or an external URL that doesn't
			// For now, we'll just log that we would delete the file
			fmt.Printf("Would delete file for episode %d: %s\n", episode.ID, episode.URL)
		}
	}

	// Delete all episodes from database
	for _, episode := range episodes {
		err = episodeService.DeleteEpisode(episode.ID)
		if err != nil {
			fmt.Printf("Warning: failed to delete episode %d: %v\n", episode.ID, err)
		}
	}

	// Delete video record
	_, err = database.DB.ID(id).Delete(&models.Video{})
	if err != nil {
		return fmt.Errorf("failed to delete video: %v", err)
	}

	return nil
}

// IncrementViews increments the view count for a video
func (s *VideoService) IncrementViews(id int64) error {
	_, err := database.DB.ID(id).Incr("views", 1).Update(&models.Video{})
	if err != nil {
		return fmt.Errorf("failed to increment views: %v", err)
	}
	return nil
}

// GetVideoStats returns video statistics
func (s *VideoService) GetVideoStats() (*models.VideoStats, error) {
	var stats models.VideoStats

	// Get total videos
	total, err := database.DB.Count(&models.Video{})
	if err != nil {
		return nil, fmt.Errorf("failed to count total videos: %v", err)
	}
	stats.TotalVideos = total

	// Get published videos
	published, err := database.DB.Where("status = ?", "published").Count(&models.Video{})
	if err != nil {
		return nil, fmt.Errorf("failed to count published videos: %v", err)
	}
	stats.PublishedVideos = published

	// Get processing videos
	processing, err := database.DB.Where("status = ?", "processing").Count(&models.Video{})
	if err != nil {
		return nil, fmt.Errorf("failed to count processing videos: %v", err)
	}
	stats.ProcessingVideos = processing

	// Get total views
	var totalViews int64
	_, err = database.DB.SQL("SELECT COALESCE(SUM(views), 0) FROM videos").Get(&totalViews)
	if err != nil {
		return nil, fmt.Errorf("failed to get total views: %v", err)
	}
	stats.TotalViews = totalViews

	// Get total storage used
	var totalStorage int64
	_, err = database.DB.SQL("SELECT COALESCE(SUM(file_size), 0) FROM videos WHERE file_size > 0").Get(&totalStorage)
	if err != nil {
		return nil, fmt.Errorf("failed to get total storage: %v", err)
	}
	stats.TotalStorage = totalStorage

	return &stats, nil
}
