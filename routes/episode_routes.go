package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"damoncoo/coremovie/middleware"
	"damoncoo/coremovie/models"
	"damoncoo/coremovie/services"
)

type EpisodeRoutes struct {
	episodeService *services.EpisodeService
}

func NewEpisodeRoutes(episodeService *services.EpisodeService) *EpisodeRoutes {
	return &EpisodeRoutes{
		episodeService: episodeService,
	}
}

func (r *EpisodeRoutes) SetupRoutes(router *gin.Engine) {
	episodeGroup := router.Group("/episodes")
	episodeGroup.Use(middleware.AuthMiddleware())
	{
		episodeGroup.POST("/", r.createEpisode)
		episodeGroup.GET("/video/:videoId", r.getEpisodesByVideoID)
		episodeGroup.GET("/:id", r.getEpisodeByID)
		episodeGroup.PUT("/:id", r.updateEpisode)
		episodeGroup.DELETE("/:id", r.deleteEpisode)
	}
}

// createEpisode creates a new episode
func (r *EpisodeRoutes) createEpisode(c *gin.Context) {
	var episode models.Episode
	if err := c.ShouldBindJSON(&episode); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := r.episodeService.CreateEpisode(&episode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, episode)
}

// getEpisodesByVideoID retrieves all episodes for a given video ID
func (r *EpisodeRoutes) getEpisodesByVideoID(c *gin.Context) {
	videoID, err := strconv.ParseInt(c.Param("videoId"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video ID"})
		return
	}

	episodes, err := r.episodeService.GetEpisodesByVideoID(videoID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, episodes)
}

// getEpisodeByID retrieves a specific episode by its ID
func (r *EpisodeRoutes) getEpisodeByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid episode ID"})
		return
	}

	episode, err := r.episodeService.GetEpisodeByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Episode not found"})
		return
	}

	c.JSON(http.StatusOK, episode)
}

// updateEpisode updates an existing episode
func (r *EpisodeRoutes) updateEpisode(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid episode ID"})
		return
	}

	episode, err := r.episodeService.GetEpisodeByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Episode not found"})
		return
	}

	if err := c.ShouldBindJSON(episode); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := r.episodeService.UpdateEpisode(episode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, episode)
}

// deleteEpisode deletes an episode by its ID
func (r *EpisodeRoutes) deleteEpisode(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid episode ID"})
		return
	}

	if err := r.episodeService.DeleteEpisode(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Episode deleted successfully"})
}